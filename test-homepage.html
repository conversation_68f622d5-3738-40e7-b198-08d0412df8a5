<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hitez - Homepage Test</title>
    <style>
        /* Basic CSS to test the scroll-snap functionality */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }

        .fullpage-container {
            height: 100vh;
            overflow-y: scroll;
            scroll-snap-type: y mandatory;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: none;
        }

        .fullpage-section {
            height: 100vh;
            scroll-snap-align: start;
            scroll-snap-stop: always;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            padding: 2rem;
            text-align: center;
        }

        .section-1 { background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%); }
        .section-2 { background: #F3F4F6; }
        .section-3 { background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%); }
        .section-4 { background: #F3F4F6; }
        .section-5 { background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%); }
        .section-6 { background: linear-gradient(135deg, #F3F4F6 0%, #F8F9FA 100%); }

        h1 { font-size: 3rem; color: #1A1A1A; margin-bottom: 1rem; }
        h2 { font-size: 2.5rem; color: #1A1A1A; margin-bottom: 1rem; }
        p { font-size: 1.2rem; color: #6B7280; max-width: 600px; }
        .accent { color: #2563EB; }

        .section-nav {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .section-nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #E5E7EB;
            border: 2px solid #FFFFFF;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .section-nav-dot:hover,
        .section-nav-dot.active {
            background: #2563EB;
            transform: scale(1.3);
        }

        @media (max-width: 768px) {
            h1 { font-size: 2rem; }
            h2 { font-size: 1.8rem; }
            p { font-size: 1rem; }
            .section-nav { right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="fullpage-container">
        <!-- Section Navigation -->
        <nav class="section-nav">
            <div class="section-nav-dot active" data-section="0"></div>
            <div class="section-nav-dot" data-section="1"></div>
            <div class="section-nav-dot" data-section="2"></div>
            <div class="section-nav-dot" data-section="3"></div>
            <div class="section-nav-dot" data-section="4"></div>
            <div class="section-nav-dot" data-section="5"></div>
        </nav>

        <!-- Hero Section -->
        <section class="fullpage-section section-1" id="section-0">
            <h1>Born Digital.<br><span class="accent">Built Creative.</span></h1>
            <p>We are a new generation of creators, ready to transform how the world experiences digital innovation.</p>
        </section>

        <!-- Problem Section -->
        <section class="fullpage-section section-2" id="section-1">
            <h2>The Digital World Needs <span class="accent">Fresh Thinking</span></h2>
            <p>Most digital experiences feel outdated before they launch. We're here to change that with innovative solutions that push boundaries.</p>
        </section>

        <!-- Vision Section -->
        <section class="fullpage-section section-3" id="section-2">
            <h2>Our <span class="accent">Vision</span> for Tomorrow</h2>
            <p>We're building the future of digital experiences, one innovation at a time. Every project is a chance to create something extraordinary.</p>
        </section>

        <!-- Innovation Section -->
        <section class="fullpage-section section-4" id="section-3">
            <h2>Innovation <span class="accent">Showcase</span></h2>
            <p>Every project is a learning lab where we push the boundaries of creative technology. These are our experiments in digital innovation.</p>
        </section>

        <!-- Services Section -->
        <section class="fullpage-section section-5" id="section-4">
            <h2>What We <span class="accent">Create</span></h2>
            <p>We specialize in experiences that blur the line between digital and physical reality, creating unforgettable moments.</p>
        </section>

        <!-- Contact Section -->
        <section class="fullpage-section section-6" id="section-5">
            <h2>Ready to Build the <span class="accent">Future?</span></h2>
            <p>Every great innovation starts with a conversation. Let's create something extraordinary together.</p>
        </section>
    </div>

    <script>
        // Simple section navigation
        const dots = document.querySelectorAll('.section-nav-dot');
        const sections = document.querySelectorAll('.fullpage-section');
        
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                sections[index].scrollIntoView({ behavior: 'smooth' });
            });
        });

        // Update active dot on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const sectionIndex = Array.from(sections).indexOf(entry.target);
                    dots.forEach(dot => dot.classList.remove('active'));
                    dots[sectionIndex].classList.add('active');
                }
            });
        }, { threshold: 0.5 });

        sections.forEach(section => observer.observe(section));
    </script>
</body>
</html>
