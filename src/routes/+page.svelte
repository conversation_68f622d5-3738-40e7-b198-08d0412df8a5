<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { getServices, getFeaturedProjects, getApproaches, getPageContent } from '$lib/cms';

	const services = getServices();
	const featuredWork = getFeaturedProjects();
	const approaches = getApproaches();
	const pageContent = getPageContent('home');
</script>

<svelte:head>
	<title>{pageContent.title}</title>
	<meta name="description" content={pageContent.description} />
</svelte:head>

<!-- Hero Section -->
<section
	class="from-primary to-surface relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br"
>
	<!-- Background Elements -->
	<div class="absolute inset-0">
		<!-- Animated background elements -->
		<div
			class="bg-accent/5 absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full blur-3xl"
		></div>
		<div
			class="bg-accent-secondary/5 absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full blur-3xl delay-1000"
		></div>
	</div>

	<div class="relative z-10 mx-auto max-w-4xl px-6 text-center">
		<h1 class="fade-in mb-6 text-6xl font-black md:text-8xl">
			<span class="text-text slide-in-left block">{pageContent.hero?.title.line1}</span>
			<span class="accent-text slide-in-right block">{pageContent.hero?.title.line2}</span>
		</h1>
		<p
			class="text-text-muted scale-in mx-auto mb-8 max-w-2xl text-xl md:text-2xl"
			style="animation-delay: 0.3s;"
		>
			{pageContent.hero?.subtitle}
		</p>
		<div class="scale-in hover-lift" style="animation-delay: 0.6s;">
			<Button href={pageContent.hero?.ctaLink} variant="primary" size="lg">
				{pageContent.hero?.ctaText}
			</Button>
		</div>
	</div>

	<!-- Scroll indicator -->
	<div class="absolute bottom-8 left-1/2 -translate-x-1/2 transform animate-bounce">
		<div class="border-accent flex h-10 w-6 justify-center rounded-full border-2">
			<div class="bg-accent mt-2 h-3 w-1 animate-pulse rounded-full"></div>
		</div>
	</div>
</section>

<!-- Who We Are Section -->
<section class="px-6 py-20">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="mb-8 text-4xl font-bold md:text-5xl">We're Not Just Another Agency.</h2>
			<p class="text-text-muted mb-8 text-xl leading-relaxed">
				We are the digital natives who grew up with technology in our DNA. While others adapt to
				change, we create it. Our Gen Z perspective brings fresh energy to every project, combined
				with the professionalism that delivers results.
			</p>
			<Button href="/about" variant="outline">More About Us →</Button>
		</div>
	</div>
</section>

<!-- Services Section -->
<section class="section-padding-lg bg-surface-elevated">
	<div class="container-wide">
		<div class="slide-up mb-20 text-center">
			<h2 class="mb-8 text-4xl font-bold md:text-5xl">
				What We <span class="accent-text">Do</span>
			</h2>
			<p class="text-text-muted mx-auto max-w-2xl text-xl">
				We specialize in creating experiences that blur the line between digital and physical
				reality.
			</p>
		</div>

		<div class="grid-auto-fit">
			{#each services as service, index}
				<div class="slide-up hover-lift hover-glow group" style="animation-delay: {index * 0.1}s;">
					<div class="glass-effect border-accent h-full p-8 transition-all duration-300">
						<div class="mb-4 text-4xl transition-transform duration-300 group-hover:scale-110">
							{service.icon}
						</div>
						<h3 class="accent-text mb-4 text-xl font-bold transition-all duration-300">
							{service.title}
						</h3>
						<p class="text-text-muted group-hover:text-text transition-colors duration-300">
							{service.description}
						</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Featured Work Section -->
<section class="section-padding-lg">
	<div class="container-wide">
		<div class="slide-up mb-20 text-center">
			<h2 class="mb-8 text-4xl font-bold md:text-5xl">
				Our Work in <span class="accent-text">Action</span>
			</h2>
			<p class="text-text-muted mx-auto max-w-2xl text-xl">
				Every project is a chance to push boundaries and create something extraordinary.
			</p>
		</div>

		<div class="grid grid-cols-1 gap-12 md:grid-cols-2">
			{#each featuredWork as work, index}
				<div class="slide-up hover-lift group hover-glow" style="animation-delay: {index * 0.1}s;">
					<div
						class="glass-effect border-accent relative cursor-pointer overflow-hidden transition-all duration-300"
					>
						<div
							class="from-accent/20 to-accent/5 relative flex aspect-video items-center justify-center overflow-hidden bg-gradient-to-br"
						>
							<div
								class="text-6xl opacity-50 transition-transform duration-500 group-hover:scale-110"
							>
								🎨
							</div>
							<div
								class="bg-accent/10 absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
							></div>
						</div>
						<div class="p-6">
							<div class="accent-text mb-2 text-sm transition-all duration-300">
								{work.category}
							</div>
							<h3
								class="group-hover:accent-text mb-2 text-xl font-bold transition-all duration-300"
							>
								{work.title}
							</h3>
							<p class="text-text-muted group-hover:text-text mb-4 transition-colors duration-300">
								{work.description}
							</p>
							<div
								class="text-accent flex items-center transition-transform duration-300 group-hover:translate-x-2"
							>
								<span class="text-sm font-medium">View Case Study</span>
								<svg
									class="ml-2 h-4 w-4 transition-transform duration-300 group-hover:rotate-45"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 5l7 7-7 7"
									></path>
								</svg>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<div class="slide-up mt-12 text-center">
			<Button href="/work" variant="outline" size="lg">View All Projects</Button>
		</div>
	</div>
</section>

<!-- Our Approach Section -->
<section class="section-padding-lg bg-surface-elevated">
	<div class="container-wide">
		<div class="slide-up mb-20 text-center">
			<h2 class="mb-8 text-4xl font-bold md:text-5xl">
				We Don't Follow Trends.<br />
				<span class="accent-text">We Create Them.</span>
			</h2>
			<p class="text-text-muted mx-auto max-w-2xl text-xl">
				Our approach is built on three core principles that set us apart from traditional agencies.
			</p>
		</div>

		<div class="grid grid-cols-1 gap-12 md:grid-cols-3">
			{#each approaches as approach, index}
				<div class="slide-up text-center" style="animation-delay: {index * 0.2}s;">
					<div
						class="glass-effect border-accent hover-glow hover-lift group h-full p-8 transition-all duration-300"
					>
						<div class="mb-6 text-5xl transition-transform duration-300 group-hover:scale-110">
							{approach.icon}
						</div>
						<h3 class="accent-text mb-4 text-2xl font-bold transition-all duration-300">
							{approach.title}
						</h3>
						<p
							class="text-text-muted group-hover:text-text leading-relaxed transition-colors duration-300"
						>
							{approach.description}
						</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Final CTA Section -->
<section
	class="section-padding-lg from-surface to-primary relative overflow-hidden bg-gradient-to-r"
>
	<!-- Background elements -->
	<div class="absolute inset-0">
		<div class="bg-accent/5 absolute top-0 left-1/4 h-96 w-96 rounded-full blur-3xl"></div>
		<div
			class="bg-accent-secondary/5 absolute right-1/4 bottom-0 h-64 w-64 rounded-full blur-2xl"
		></div>
	</div>

	<div class="container-narrow relative z-10 text-center">
		<div class="slide-up">
			<h2 class="mb-6 text-5xl font-black md:text-6xl">
				{@html pageContent.cta?.title}
			</h2>
			<p class="text-text-muted mx-auto mb-8 max-w-2xl text-2xl">
				{pageContent.cta?.subtitle}
			</p>
			<div class="space-y-4 md:flex md:justify-center md:space-y-0 md:space-x-4">
				<Button href={pageContent.cta?.primaryCTA.link} variant="primary" size="lg">
					{pageContent.cta?.primaryCTA.text}
				</Button>
				<Button href={pageContent.cta?.secondaryCTA.link} variant="outline" size="lg">
					{pageContent.cta?.secondaryCTA.text}
				</Button>
			</div>
		</div>
	</div>
</section>
