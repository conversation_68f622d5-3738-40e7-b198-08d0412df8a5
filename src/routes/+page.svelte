<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import FullViewportSection from '$lib/components/FullViewportSection.svelte';
	import SectionNavigation from '$lib/components/SectionNavigation.svelte';
	import { getServices, getPageContent } from '$lib/cms';

	const services = getServices();
	const pageContent = getPageContent('home');

	const sections = [
		{ id: 'hero', label: 'Home' },
		{ id: 'problem', label: 'Challenge' },
		{ id: 'vision', label: 'Vision' },
		{ id: 'demos', label: 'Innovation' },
		{ id: 'services', label: 'Services' },
		{ id: 'contact', label: 'Contact' }
	];
</script>

<svelte:head>
	<title>{pageContent.title}</title>
	<meta name="description" content={pageContent.description} />
</svelte:head>

<div class="fullpage-container">
	<!-- Section Navigation -->
	<SectionNavigation {sections} />

	<!-- Hero Section -->
	<FullViewportSection
		id="hero"
		background="linear-gradient(135deg, var(--color-primary) 0%, var(--color-surface) 100%)"
	>
		<div slot="default" class="text-center">
			<!-- Background Elements -->
			<div class="pointer-events-none absolute inset-0">
				<div
					class="bg-accent/5 absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full blur-3xl"
				></div>
				<div
					class="bg-accent-secondary/5 absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full blur-3xl delay-1000"
				></div>
			</div>

			<div class="relative z-10 mx-auto max-w-4xl">
				<h1 class="section-transition mb-6 text-6xl font-black md:text-8xl">
					<span class="text-text block">{pageContent.hero?.title.line1}</span>
					<span class="accent-text block">{pageContent.hero?.title.line2}</span>
				</h1>
				<p class="section-transition text-text-muted mx-auto mb-8 max-w-2xl text-xl md:text-2xl">
					{pageContent.hero?.subtitle}
				</p>
				<div class="section-transition">
					<Button href="#vision" variant="primary" size="lg">
						{pageContent.hero?.ctaText}
					</Button>
				</div>
			</div>

			<!-- Scroll indicator -->
			<div class="absolute bottom-8 left-1/2 -translate-x-1/2 transform animate-bounce">
				<div class="border-accent flex h-10 w-6 justify-center rounded-full border-2">
					<div class="bg-accent mt-2 h-3 w-1 animate-pulse rounded-full"></div>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Problem/Solution Section -->
	<FullViewportSection id="problem" className="bg-surface-elevated">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition mb-8 text-4xl font-bold md:text-5xl">
					{@html pageContent.problem?.title}
				</h2>
				<p class="section-transition text-text-muted mx-auto mb-16 max-w-2xl text-xl">
					{pageContent.problem?.subtitle}
				</p>

				<!-- Problem Points -->
				<div class="mb-16 grid grid-cols-1 gap-8 md:grid-cols-3">
					{#each pageContent.problem?.points || [] as point, index}
						<div
							class="section-transition glass-effect rounded-xl p-8"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-4 text-4xl">{point.icon}</div>
							<h3 class="accent-text mb-4 text-xl font-bold">{point.title}</h3>
							<p class="text-text-muted">{point.description}</p>
						</div>
					{/each}
				</div>

				<!-- Solution -->
				<div class="section-transition glass-effect mx-auto max-w-4xl rounded-xl p-8">
					<h3 class="accent-text mb-4 text-2xl font-bold">
						{pageContent.problem?.solution?.title}
					</h3>
					<p class="text-text-muted text-lg leading-relaxed">
						{pageContent.problem?.solution?.description}
					</p>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Vision Section -->
	<FullViewportSection id="vision">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition mb-8 text-4xl font-bold md:text-5xl">
					{@html pageContent.vision?.title}
				</h2>
				<p class="section-transition text-text-muted mx-auto mb-8 max-w-2xl text-xl">
					{pageContent.vision?.subtitle}
				</p>
				<p class="section-transition text-accent mx-auto mb-16 max-w-3xl text-2xl font-semibold">
					{pageContent.vision?.mission}
				</p>

				<!-- Values -->
				<div class="mb-16 grid grid-cols-1 gap-8 md:grid-cols-3">
					{#each pageContent.vision?.values || [] as value, index}
						<div
							class="section-transition glass-effect hover-lift rounded-xl p-8"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-4 text-4xl">{value.icon}</div>
							<h3 class="accent-text mb-4 text-xl font-bold">{value.title}</h3>
							<p class="text-text-muted">{value.description}</p>
						</div>
					{/each}
				</div>

				<!-- Team Section -->
				<div class="section-transition glass-effect rounded-xl p-8">
					<h3 class="mb-4 text-2xl font-bold">{pageContent.vision?.team?.title}</h3>
					<p class="text-text-muted mb-8 text-lg">{pageContent.vision?.team?.description}</p>

					<div class="grid grid-cols-1 gap-8 md:grid-cols-3">
						{#each pageContent.vision?.team?.stats || [] as stat, index}
							<div class="text-center" style="animation-delay: {index * 0.1}s;">
								<div class="accent-text mb-2 text-4xl font-black">
									{stat.number}{stat.suffix}
								</div>
								<div class="text-text-muted font-medium">{stat.label}</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Demos Section -->
	<FullViewportSection id="demos" className="bg-surface-elevated">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition mb-8 text-4xl font-bold md:text-5xl">
					{@html pageContent.demos?.title}
				</h2>
				<p class="section-transition text-text-muted mx-auto mb-4 max-w-2xl text-xl">
					{pageContent.demos?.subtitle}
				</p>
				<p class="section-transition text-accent mx-auto mb-16 max-w-3xl text-lg">
					{pageContent.demos?.description}
				</p>

				<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
					{#each pageContent.demos?.featured || [] as demo, index}
						<div
							class="section-transition glass-effect hover-lift group overflow-hidden rounded-xl"
							style="animation-delay: {index * 0.1}s;"
						>
							<div
								class="from-accent/20 to-accent/5 relative flex aspect-video items-center justify-center bg-gradient-to-br"
							>
								<div
									class="text-6xl opacity-50 transition-transform duration-500 group-hover:scale-110"
								>
									🚀
								</div>
								<div
									class="bg-accent/10 absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
								></div>
							</div>

							<div class="p-6">
								<div class="accent-text mb-2 text-sm font-medium">{demo.category}</div>
								<h3 class="group-hover:accent-text mb-3 text-xl font-bold transition-colors">
									{demo.title}
								</h3>
								<p class="text-text-muted mb-4 text-sm leading-relaxed">{demo.description}</p>

								<div class="mb-4">
									<div class="text-accent mb-2 text-xs font-semibold">INNOVATION:</div>
									<p class="text-text-muted text-xs">{demo.innovation}</p>
								</div>

								<div class="mb-4">
									<div class="text-accent mb-2 text-xs font-semibold">TECHNOLOGIES:</div>
									<div class="flex flex-wrap gap-1">
										{#each demo.technologies as tech}
											<span class="bg-accent/10 text-accent rounded px-2 py-1 text-xs">{tech}</span>
										{/each}
									</div>
								</div>

								<div>
									<div class="text-accent mb-2 text-xs font-semibold">KEY LEARNINGS:</div>
									<ul class="text-text-muted space-y-1 text-xs">
										{#each demo.learnings as learning}
											<li>• {learning}</li>
										{/each}
									</ul>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Services Section -->
	<FullViewportSection id="services">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition mb-8 text-4xl font-bold md:text-5xl">
					What We <span class="accent-text">Create</span>
				</h2>
				<p class="section-transition text-text-muted mx-auto mb-16 max-w-2xl text-xl">
					We specialize in experiences that blur the line between digital and physical reality.
				</p>

				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
					{#each services as service, index}
						<div
							class="section-transition glass-effect hover-lift group rounded-xl p-6"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-4 text-3xl transition-transform duration-300 group-hover:scale-110">
								{service.icon}
							</div>
							<h3 class="accent-text mb-3 text-lg font-bold transition-all duration-300">
								{service.title}
							</h3>
							<p
								class="text-text-muted group-hover:text-text text-sm transition-colors duration-300"
							>
								{service.description}
							</p>
						</div>
					{/each}
				</div>

				<div class="section-transition mt-12">
					<Button href="/services" variant="outline" size="lg">Explore Our Services</Button>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Final CTA Section -->
	<FullViewportSection
		id="contact"
		background="linear-gradient(135deg, var(--color-surface) 0%, var(--color-primary) 100%)"
	>
		<div slot="default" class="text-center">
			<!-- Background elements -->
			<div class="pointer-events-none absolute inset-0">
				<div class="bg-accent/5 absolute top-0 left-1/4 h-96 w-96 rounded-full blur-3xl"></div>
				<div
					class="bg-accent-secondary/5 absolute right-1/4 bottom-0 h-64 w-64 rounded-full blur-2xl"
				></div>
			</div>

			<div class="relative z-10 mx-auto max-w-4xl">
				<h2 class="section-transition mb-6 text-5xl font-black md:text-6xl">
					{@html pageContent.cta?.title}
				</h2>
				<p class="section-transition text-text-muted mx-auto mb-8 max-w-2xl text-2xl">
					{pageContent.cta?.subtitle}
				</p>
				<div
					class="section-transition space-y-4 md:flex md:justify-center md:space-y-0 md:space-x-4"
				>
					<Button href={pageContent.cta?.primaryCTA.link} variant="primary" size="lg">
						{pageContent.cta?.primaryCTA.text}
					</Button>
					<Button href={pageContent.cta?.secondaryCTA.link} variant="outline" size="lg">
						{pageContent.cta?.secondaryCTA.text}
					</Button>
				</div>
			</div>
		</div>
	</FullViewportSection>
</div>
