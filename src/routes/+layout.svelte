<script lang="ts">
	import '../app.css';
	import Header from '$lib/components/Header.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import { onMount } from 'svelte';

	let { children } = $props();
	let observer: IntersectionObserver | null = null;

	// Initialize scroll animations
	function initializeAnimations() {
		// Disconnect existing observer if it exists
		if (observer) {
			observer.disconnect();
		}

		const observerOptions = {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		};

		observer = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					entry.target.classList.add('visible');
				}
			});
		}, observerOptions);

		// Observe all elements with animation classes
		const animatedElements = document.querySelectorAll('.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in');
		animatedElements.forEach((el) => observer?.observe(el));
	}

	onMount(() => {
		initializeAnimations();

		// Set up a MutationObserver to watch for DOM changes
		const mutationObserver = new MutationObserver((mutations) => {
			let shouldReinitialize = false;

			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					// Check if any added nodes contain animation classes
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === Node.ELEMENT_NODE) {
							const element = node as Element;
							if (element.matches && (
								element.matches('.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in') ||
								element.querySelector('.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in')
							)) {
								shouldReinitialize = true;
							}
						}
					});
				}
			});

			if (shouldReinitialize) {
				// Use a small delay to ensure DOM has fully updated
				setTimeout(() => {
					initializeAnimations();
				}, 50);
			}
		});

		// Start observing the document for changes
		mutationObserver.observe(document.body, {
			childList: true,
			subtree: true
		});

		return () => {
			if (observer) {
				observer.disconnect();
			}
			mutationObserver.disconnect();
		};
	});
</script>

<div class="min-h-screen flex flex-col">
	<Header />

	<main class="flex-grow">
		{@render children()}
	</main>

	<Footer />
</div>
